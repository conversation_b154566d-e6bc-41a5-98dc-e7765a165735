# 豆包API密钥配置说明

## 配置步骤

1. **获取豆包API密钥**
   - 访问字节跳动豆包开放平台
   - 注册账号并创建应用
   - 获取API密钥

2. **配置API密钥**
   - 打开项目根目录下的 `doubao_api.txt` 文件
   - 删除注释行
   - 直接粘贴您的API密钥（不需要引号或其他格式）
   - 保存文件

3. **验证配置**
   - 确保 `doubao_api.txt` 文件只包含API密钥，无其他内容
   - 运行测试：`python block_parsor.py`

## 安全提醒

- `doubao_api.txt` 文件已被添加到 `.gitignore`，不会被提交到版本控制
- 请勿在代码中硬编码API密钥
- 请勿将API密钥分享给他人

## 当前配置状态

- ✅ 默认模型：Doubao
- ✅ API文件路径：doubao_api.txt  
- ✅ 安全配置：已添加到.gitignore
- ⏳ 待完成：填入您的API密钥

## 模型切换（可选）

如需使用其他模型，请修改以下文件：
- `block_parsor.py` 第7行和第319行
- `html_generator.py` 第400行

支持的模型：Doubao, Qwen, GPT, Gemini
